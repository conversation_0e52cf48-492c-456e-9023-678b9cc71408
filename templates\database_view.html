<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gas Prices Database - Gas Bottle Price Scraper</title>
    <link rel="icon" href="/static/media/favicon.ico" type="image/x-icon">
    <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Loading indicators */
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: inline-block;
        }

        /* Multi-select dropdown styles */
        .multi-select {
            position: relative;
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            max-height: 200px;
            overflow-y: auto;
            z-index: 50;
            display: none;
        }

        .multi-select-dropdown.show {
            display: block;
        }

        .multi-select-option {
            padding: 0.5rem;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }

        .multi-select-option:hover {
            background-color: #f9fafb;
        }

        .multi-select-option.selected {
            background-color: #dbeafe;
            color: #1d4ed8;
        }

        .multi-select-display {
            min-height: 2.5rem;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            background: white;
            cursor: pointer;
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            align-items: center;
        }

        .multi-select-tag {
            background-color: #3b82f6;
            color: white;
            padding: 0.125rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .multi-select-tag .remove {
            cursor: pointer;
            font-weight: bold;
        }

        .multi-select-placeholder {
            color: #9ca3af;
            font-size: 0.875rem;
        }

        .filter-loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .record-count {
            font-size: 0.875rem;
            color: #6b7280;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Authentication Header -->
    {% include "auth_header.html" %}
    <div class="container mx-auto px-4 py-8">

        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600">Gas Prices Database</h1>
            <p class="text-center text-gray-600 mt-2">Historical gas price data</p>
        </header>

        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <!-- Filters -->
            <form id="filter-form" hx-get="/database-table" hx-target="#table-body" hx-trigger="load, submit">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                        <input type="date" id="start_date" name="start_date"
                               value="{{ request.query_params.get('start_date', (datetime.now() - timedelta(days=30)).date().isoformat()) }}"
                               data-initial="{{ request.query_params.get('start_date', (datetime.now() - timedelta(days=30)).date().isoformat()) }}"
                               class="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               onchange="updateFilters()">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                        <input type="date" id="end_date" name="end_date"
                               value="{{ request.query_params.get('end_date', datetime.now().date().isoformat()) }}"
                               data-initial="{{ request.query_params.get('end_date', datetime.now().date().isoformat()) }}"
                               class="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               onchange="updateFilters()">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Countries
                            <span class="record-count" id="countries-count">({{ countries|length }})</span>
                        </label>
                        <div class="multi-select" id="countries-multi-select">
                            <div class="multi-select-display" onclick="toggleDropdown('countries')">
                                <span class="multi-select-placeholder" id="countries-placeholder">Select countries...</span>
                            </div>
                            <div class="multi-select-dropdown" id="countries-dropdown">
                                <div class="multi-select-option" onclick="selectAll('countries')">
                                    <strong>Select All</strong>
                                </div>
                                <div class="multi-select-option" onclick="clearAll('countries')">
                                    <strong>Clear All</strong>
                                </div>
                                <hr>
                                {% for country_name in countries %}
                                <div class="multi-select-option" data-value="{{ country_name }}" onclick="toggleOption('countries', '{{ country_name }}')">
                                    {{ country_name }}
                                </div>
                                {% endfor %}
                            </div>
                            <input type="hidden" id="countries" name="countries" value="">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Companies
                            <span class="record-count" id="companies-count">({{ companies|length }})</span>
                        </label>
                        <div class="multi-select" id="companies-multi-select">
                            <div class="multi-select-display" onclick="toggleDropdown('companies')">
                                <span class="multi-select-placeholder" id="companies-placeholder">Select companies...</span>
                            </div>
                            <div class="multi-select-dropdown" id="companies-dropdown">
                                <div class="multi-select-option" onclick="selectAll('companies')">
                                    <strong>Select All</strong>
                                </div>
                                <div class="multi-select-option" onclick="clearAll('companies')">
                                    <strong>Clear All</strong>
                                </div>
                                <hr>
                                {% for company_name in companies %}
                                <div class="multi-select-option" data-value="{{ company_name }}" onclick="toggleOption('companies', '{{ company_name }}')">
                                    {{ company_name }}
                                </div>
                                {% endfor %}
                            </div>
                            <input type="hidden" id="companies" name="companies" value="">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Product Types
                            <span class="record-count" id="product-types-count">({{ product_types|length }})</span>
                        </label>
                        <div class="multi-select" id="product-types-multi-select">
                            <div class="multi-select-display" onclick="toggleDropdown('product-types')">
                                <span class="multi-select-placeholder" id="product-types-placeholder">Select product types...</span>
                            </div>
                            <div class="multi-select-dropdown" id="product-types-dropdown">
                                <div class="multi-select-option" onclick="selectAll('product-types')">
                                    <strong>Select All</strong>
                                </div>
                                <div class="multi-select-option" onclick="clearAll('product-types')">
                                    <strong>Clear All</strong>
                                </div>
                                <hr>
                                {% for type in product_types %}
                                <div class="multi-select-option" data-value="{{ type }}" onclick="toggleOption('product-types', '{{ type }}')">
                                    {{ type }}
                                </div>
                                {% endfor %}
                            </div>
                            <input type="hidden" id="product_types" name="product_types" value="">
                        </div>
                    </div>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-4">
                        <button
                            hx-get="/generate-report"
                            hx-target="#download-trigger"
                            hx-include="#filter-form"
                            hx-indicator="#export-indicator"
                            class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-300 relative"
                        >
                            Export Report (Excel)
                            <span id="export-indicator" class="htmx-indicator ml-2">
                                <svg class="animate-spin h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button>
                        <!--button
                            hx-get="/generate-email-report"
                            hx-target="#email-report-status"
                            hx-include="#filter-form"
                            hx-indicator="#email-indicator"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-300 relative"
                        >
                            Send Email Report
                            <span id="email-indicator" class="htmx-indicator ml-2">
                                <svg class="animate-spin h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button-->
                    </div>
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                            Apply Filters
                            <span id="filter-indicator" class="htmx-indicator ml-2">
                                <svg class="animate-spin h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button>
                        <button
                            type="button"
                            onclick="resetAllFilters()"
                            class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                            Reset Filters
                        </button>
                    </div>
                </div>
            </form>

            <!-- Data Table -->
            <div class="overflow-x-auto mt-6">
                <div id="table-body">
                    {% include "database_table.html" %}
                </div>
            </div>
            <!-- Add this div for handling downloads -->
            <div id="download-trigger"></div>

            <!-- Add this div for email report status -->
            <div id="email-report-status" class="mt-4"></div>
        </div>
    </div>

    <!-- Include toast notifications -->
    {% include "toast_notifications.html" %}

    <!-- Job status indicator in the corner - only loaded once when a job starts -->
    <div id="job-indicator-container" hx-get="/job-indicator" hx-trigger="load"></div>

    <script>
        // Multi-select functionality
        let selectedValues = {
            'countries': new Set(),
            'companies': new Set(),
            'product-types': new Set()
        };

        // Toggle dropdown visibility
        function toggleDropdown(filterType) {
            const dropdown = document.getElementById(filterType + '-dropdown');
            const isVisible = dropdown.classList.contains('show');

            // Close all dropdowns first
            document.querySelectorAll('.multi-select-dropdown').forEach(d => d.classList.remove('show'));

            // Toggle current dropdown
            if (!isVisible) {
                dropdown.classList.add('show');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.multi-select')) {
                document.querySelectorAll('.multi-select-dropdown').forEach(d => d.classList.remove('show'));
            }
        });

        // Toggle option selection
        function toggleOption(filterType, value) {
            const option = document.querySelector(`#${filterType}-dropdown [data-value="${value}"]`);

            if (selectedValues[filterType].has(value)) {
                selectedValues[filterType].delete(value);
                option.classList.remove('selected');
            } else {
                selectedValues[filterType].add(value);
                option.classList.add('selected');
            }

            updateDisplay(filterType);
            updateHiddenInput(filterType);
            updateFilters();
        }

        // Select all options
        function selectAll(filterType) {
            const options = document.querySelectorAll(`#${filterType}-dropdown [data-value]`);
            selectedValues[filterType].clear();

            options.forEach(option => {
                const value = option.getAttribute('data-value');
                selectedValues[filterType].add(value);
                option.classList.add('selected');
            });

            updateDisplay(filterType);
            updateHiddenInput(filterType);
            updateFilters();
        }

        // Clear all options
        function clearAll(filterType) {
            selectedValues[filterType].clear();

            const options = document.querySelectorAll(`#${filterType}-dropdown [data-value]`);
            options.forEach(option => option.classList.remove('selected'));

            updateDisplay(filterType);
            updateHiddenInput(filterType);
            updateFilters();
        }

        // Update display with selected tags
        function updateDisplay(filterType) {
            const display = document.querySelector(`#${filterType}-multi-select .multi-select-display`);
            const placeholder = document.getElementById(filterType + '-placeholder');

            // Clear current display
            display.innerHTML = '';

            if (selectedValues[filterType].size === 0) {
                const placeholderText = filterType === 'countries' ? 'Select countries...' :
                                      filterType === 'companies' ? 'Select companies...' :
                                      'Select product types...';
                display.innerHTML = `<span class="multi-select-placeholder">${placeholderText}</span>`;
            } else {
                selectedValues[filterType].forEach(value => {
                    const tag = document.createElement('span');
                    tag.className = 'multi-select-tag';
                    tag.innerHTML = `${value} <span class="remove" onclick="removeTag('${filterType}', '${value}')">&times;</span>`;
                    display.appendChild(tag);
                });
            }
        }

        // Remove individual tag
        function removeTag(filterType, value) {
            selectedValues[filterType].delete(value);

            const option = document.querySelector(`#${filterType}-dropdown [data-value="${value}"]`);
            if (option) option.classList.remove('selected');

            updateDisplay(filterType);
            updateHiddenInput(filterType);
            updateFilters();
        }

        // Update hidden input with selected values
        function updateHiddenInput(filterType) {
            const inputId = filterType === 'countries' ? 'countries' :
                           filterType === 'companies' ? 'companies' :
                           'product_types';

            const input = document.getElementById(inputId);
            input.value = Array.from(selectedValues[filterType]).join(',');
        }

        // Reset all filters
        function resetAllFilters() {
            // Reset date inputs
            document.getElementById('start_date').value = document.getElementById('start_date').dataset.initial;
            document.getElementById('end_date').value = document.getElementById('end_date').dataset.initial;

            // Clear all multi-select filters
            ['countries', 'companies', 'product-types'].forEach(filterType => {
                clearAll(filterType);
            });

            // Trigger form submission
            htmx.trigger('#filter-form', 'submit');
        }

        // Update filters with cascading effect
        function updateFilters() {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            const countries = Array.from(selectedValues['countries']).join(',');
            const companies = Array.from(selectedValues['companies']).join(',');

            // Show loading state
            document.querySelectorAll('.multi-select').forEach(select => {
                select.classList.add('filter-loading');
            });

            // Update companies based on selected countries
            if (selectedValues['countries'].size > 0) {
                fetch(`/api/filter-companies?start_date=${startDate}&end_date=${endDate}&countries=${countries}`)
                    .then(response => response.json())
                    .then(data => {
                        updateFilterOptions('companies', data.companies);
                        document.getElementById('companies-count').textContent = `(${data.count})`;
                    })
                    .catch(error => console.error('Error updating companies:', error));
            }

            // Update product types based on selected countries and companies
            if (selectedValues['countries'].size > 0 || selectedValues['companies'].size > 0) {
                fetch(`/api/filter-product-types?start_date=${startDate}&end_date=${endDate}&countries=${countries}&companies=${companies}`)
                    .then(response => response.json())
                    .then(data => {
                        updateFilterOptions('product-types', data.product_types);
                        document.getElementById('product-types-count').textContent = `(${data.count})`;
                    })
                    .catch(error => console.error('Error updating product types:', error));
            }

            // Remove loading state
            setTimeout(() => {
                document.querySelectorAll('.multi-select').forEach(select => {
                    select.classList.remove('filter-loading');
                });
            }, 500);

            // Trigger table update
            htmx.trigger('#filter-form', 'submit');
        }

        // Update filter options dynamically
        function updateFilterOptions(filterType, newOptions) {
            const dropdown = document.getElementById(filterType + '-dropdown');
            const currentSelected = selectedValues[filterType];

            // Clear existing options (except control buttons)
            const existingOptions = dropdown.querySelectorAll('[data-value]');
            existingOptions.forEach(option => option.remove());

            // Add new options
            newOptions.forEach(option => {
                const optionElement = document.createElement('div');
                optionElement.className = 'multi-select-option';
                optionElement.setAttribute('data-value', option);
                optionElement.textContent = option;
                optionElement.onclick = () => toggleOption(filterType, option);

                if (currentSelected.has(option)) {
                    optionElement.classList.add('selected');
                }

                dropdown.appendChild(optionElement);
            });

            // Remove selected values that are no longer available
            const availableOptions = new Set(newOptions);
            currentSelected.forEach(value => {
                if (!availableOptions.has(value)) {
                    currentSelected.delete(value);
                }
            });

            updateDisplay(filterType);
            updateHiddenInput(filterType);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any pre-selected values from URL parameters
            const urlParams = new URLSearchParams(window.location.search);

            ['countries', 'companies', 'product_types'].forEach(param => {
                const value = urlParams.get(param);
                if (value) {
                    const filterType = param === 'product_types' ? 'product-types' : param;
                    const values = value.split(',');
                    values.forEach(v => {
                        if (v.trim()) {
                            selectedValues[filterType].add(v.trim());
                            const option = document.querySelector(`#${filterType}-dropdown [data-value="${v.trim()}"]`);
                            if (option) option.classList.add('selected');
                        }
                    });
                    updateDisplay(filterType);
                    updateHiddenInput(filterType);
                }
            });
        });

        // HTMX event listeners for toast notifications
        document.addEventListener('htmx:beforeRequest', function(evt) {
            if (evt.detail.requestConfig.path === '/generate-report') {
                window.toastManager.info('Generating Report', 'Processing gas bottle prices...', { duration: 0 });
            } else if (evt.detail.requestConfig.path === '/generate-email-report') {
                window.toastManager.info('Sending Email', 'Generating and sending report...', { duration: 0 });
            }
        });

        document.addEventListener('htmx:afterRequest', function(evt) {
            // Clear any existing info toasts when request completes
            if (window.toastManager) {
                window.toastManager.toasts.forEach((toast, id) => {
                    if (toast.classList.contains('info')) {
                        window.toastManager.hide(id);
                    }
                });
            }

            if (evt.detail.requestConfig.path === '/generate-report') {
                if (evt.detail.xhr.status === 200) {
                    // Success toast will be triggered by the script in the response
                } else {
                    window.toastManager.error('Report Generation Failed', 'There was an error generating the report. Please try again.');
                }
            } else if (evt.detail.requestConfig.path === '/generate-email-report') {
                if (evt.detail.xhr.status === 200) {
                    // Success toast will be triggered by the script in the response
                } else {
                    window.toastManager.error('Email Failed', 'There was an error sending the email report. Please try again.');
                }
            }
        });
    </script>
</body>
</html>

