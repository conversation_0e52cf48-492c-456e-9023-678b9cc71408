
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Guide - <PERSON>ttle Price Scraper</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
            color: #333;
            background-color: #fff;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 0.3rem;
        }
        
        p {
            margin-bottom: 1rem;
            text-align: justify;
        }
        
        ul, ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }
        
        li {
            margin-bottom: 0.5rem;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Courier New', Consolas, Monaco, monospace;
            font-size: 0.9em;
        }
        
        code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 0.2rem 0.4rem;
            font-family: 'Courier New', Consolas, Monaco, monospace;
            font-size: 0.9em;
        }
        
        pre code {
            background-color: transparent;
            border: none;
            padding: 0;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1rem 0;
            border: 1px solid #ddd;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #666;
            font-style: italic;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        hr {
            border: none;
            border-top: 1px solid #bdc3c7;
            margin: 2rem 0;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    {% include "auth_header.html" %}
    <h1 id="user-guide-gas-bottle-price-scraper">User Guide - Gas Bottle Price Scraper</h1>
<p>This guide explains how to use the Gas Bottle Price Scraper web interface and features.</p>
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li><a href="#getting-started">Getting Started</a></li>
<li><a href="#web-interface-overview">Web Interface Overview</a></li>
<li><a href="#viewing-price-data">Viewing Price Data</a></li>
<li><a href="#running-manual-scraping">Running Manual Scraping</a></li>
<li><a href="#generating-reports">Generating Reports</a></li>
<li><a href="#admin-panel">Admin Panel</a></li>
<li><a href="#understanding-the-data">Understanding the Data</a></li>
<li><a href="#troubleshooting">Troubleshooting</a></li>
</ol>
<h2 id="getting-started">Getting Started</h2>
<h3 id="accessing-the-application">Accessing the Application</h3>
<ol>
<li>Open your web browser</li>
<li>Navigate to the application URL (e.g., <code>http://localhost:8000</code>)</li>
<li>You'll be automatically redirected to the database view</li>
</ol>
<h3 id="system-requirements">System Requirements</h3>
<ul>
<li>Modern web browser (Chrome, Firefox, Safari, Edge)</li>
<li>Internet connection for real-time data</li>
<li>JavaScript enabled for interactive features</li>
</ul>
<h2 id="web-interface-overview">Web Interface Overview</h2>
<p>The application provides several main sections:</p>
<h3 id="navigation-menu">Navigation Menu</h3>
<ul>
<li><strong>Database</strong>: View and filter price data</li>
<li><strong>Manual Request</strong>: Trigger scraping manually</li>
<li><strong>Results</strong>: View historical scraping results</li>
<li><strong>Admin</strong>: Configuration and scheduling (requires authentication)</li>
</ul>
<h3 id="key-features">Key Features</h3>
<ul>
<li><strong>Real-time Data</strong>: Live price information from multiple sources</li>
<li><strong>Interactive Filtering</strong>: Filter by country, company, product type, and date</li>
<li><strong>Export Capabilities</strong>: Generate Excel reports with charts</li>
<li><strong>Responsive Design</strong>: Works on desktop, tablet, and mobile devices</li>
</ul>
<h2 id="viewing-price-data">Viewing Price Data</h2>
<h3 id="database-view">Database View</h3>
<p>The main database view shows current gas bottle prices with the following information:</p>
<table>
<thead>
<tr>
<th>Column</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>Country</td>
<td>Source country (Portugal, Spain, France, etc.)</td>
</tr>
<tr>
<td>Company</td>
<td>Gas company name (Rubis, Galp, Repsol, etc.)</td>
</tr>
<tr>
<td>Product</td>
<td>Gas type (Butane or Propane)</td>
</tr>
<tr>
<td>Weight</td>
<td>Bottle weight in kg</td>
</tr>
<tr>
<td>Price</td>
<td>Current price in euros</td>
</tr>
<tr>
<td>Price/kg</td>
<td>Price per kilogram</td>
</tr>
<tr>
<td>Date</td>
<td>When the price was recorded</td>
</tr>
</tbody>
</table>
<h3 id="filtering-data">Filtering Data</h3>
<p>Use the filter controls at the top of the page:</p>
<ol>
<li><strong>Date Range</strong>: Select start and end dates</li>
<li>Default shows last 30 days</li>
<li>
<p>Use date pickers for custom ranges</p>
</li>
<li>
<p><strong>Country Filter</strong>: Choose specific countries</p>
</li>
<li>Select from dropdown list</li>
<li>
<p>Multiple countries can be selected</p>
</li>
<li>
<p><strong>Company Filter</strong>: Filter by gas company</p>
</li>
<li>Shows only companies with available data</li>
<li>
<p>Useful for price comparisons</p>
</li>
<li>
<p><strong>Product Type Filter</strong>: Filter by gas type</p>
</li>
<li>Butane: Commonly used for cooking</li>
<li>Propane: Used for heating and outdoor equipment</li>
</ol>
<h3 id="sorting-and-search">Sorting and Search</h3>
<ul>
<li>Click column headers to sort data</li>
<li>Use browser search (Ctrl+F) to find specific items</li>
<li>Data automatically updates with new scraping results</li>
</ul>
<h2 id="running-manual-scraping-admin-only">Running Manual Scraping (Admin Only)</h2>
<h3 id="starting-a-scraping-job">Starting a Scraping Job</h3>
<ol>
<li>Navigate to "Manual Request" page</li>
<li>Click "Start Scraping" button</li>
<li>Monitor progress in real-time</li>
<li>View results when completed</li>
</ol>
<h3 id="job-status-indicators">Job Status Indicators</h3>
<ul>
<li><strong>Running</strong>: Scraping in progress (blue indicator)</li>
<li><strong>Completed</strong>: Successfully finished (green indicator)</li>
<li><strong>Failed</strong>: Error occurred (red indicator)</li>
</ul>
<h3 id="what-gets-scraped">What Gets Scraped</h3>
<p>The scraper automatically visits configured websites and extracts:
- Current gas bottle prices
- Product specifications
- Company information
- Regional pricing variations</p>
<h3 id="scraping-duration">Scraping Duration</h3>
<p>Typical scraping times:
- Single country: 2-5 minutes
- All countries: 10-15 minutes
- Depends on website response times</p>
<h2 id="generating-reports">Generating Reports</h2>
<h3 id="excel-report-generation">Excel Report Generation</h3>
<ol>
<li>Go to Database view</li>
<li>Apply desired filters (optional)</li>
<li>Click "Generate Report" button</li>
<li>Download automatically starts</li>
</ol>
<h3 id="report-contents">Report Contents</h3>
<p>The Excel report includes:</p>
<p><strong>Summary Sheet</strong>:
- Total items scraped
- Countries and companies covered
- Price statistics (min, max, average)
- Date range of data</p>
<p><strong>Data Sheet</strong>:
- Complete filtered dataset
- All columns from database view
- Formatted for analysis</p>
<p><strong>Charts Sheet</strong>:
- Price comparison charts
- Country-wise price distribution
- Company comparison graphs
- Trend analysis (if date range selected)</p>
<h3 id="report-customization">Report Customization</h3>
<p>Filter data before generating reports to customize content:
- <strong>Country-specific reports</strong>: Filter by single country
- <strong>Company comparisons</strong>: Select multiple companies
- <strong>Product analysis</strong>: Filter by Butane or Propane
- <strong>Time-based analysis</strong>: Use date range filters</p>
<h2 id="admin-panel">Admin Panel</h2>
<h3 id="accessing-admin-features">Accessing Admin Features</h3>
<ol>
<li>Navigate to <code>/admin</code> URL</li>
<li>Enter admin credentials when prompted</li>
<li>Access configuration and scheduling features</li>
</ol>
<p><strong>Note</strong>: Admin access requires authentication with configured username/password.</p>
<h3 id="configuration-management">Configuration Management</h3>
<p><strong>Scheduler Settings</strong>:
- Set automatic scraping frequency
- Configure scraping times
- Enable/disable automatic runs</p>
<p><strong>Site Configuration</strong>:
- Add new websites to scrape
- Modify existing site settings
- Test configuration changes</p>
<h3 id="monitoring-and-logs">Monitoring and Logs</h3>
<p><strong>System Health</strong>:
- View application status
- Check database connectivity
- Monitor system resources</p>
<p><strong>Scraping History</strong>:
- View past scraping jobs
- Check success/failure rates
- Analyze performance metrics</p>
<h2 id="understanding-the-data">Understanding the Data</h2>
<h3 id="price-information">Price Information</h3>
<p><strong>Base Price</strong>: Listed price from company website
<strong>Price per kg</strong>: Calculated rate for comparison
<strong>Regional Variations</strong>: Prices may vary by location within countries</p>
<h3 id="data-accuracy">Data Accuracy</h3>
<ul>
<li>Prices updated through automated scraping</li>
<li>Data reflects website information at time of scraping</li>
<li>Some sites may have regional pricing differences</li>
<li>Prices include applicable taxes and fees</li>
</ul>
<h3 id="currency-and-units">Currency and Units</h3>
<ul>
<li>All prices displayed in Euros (€)</li>
<li>Weights shown in kilograms (kg)</li>
<li>Dates in YYYY-MM-DD format</li>
<li>Times in 24-hour format</li>
</ul>
<h3 id="data-freshness">Data Freshness</h3>
<ul>
<li><strong>Real-time</strong>: Data from latest scraping run</li>
<li><strong>Historical</strong>: Previous scraping results available</li>
<li><strong>Update Frequency</strong>: Configurable (default: hourly)</li>
<li><strong>Data Retention</strong>: Configurable (default: 30 days)</li>
</ul>
<h2 id="troubleshooting">Troubleshooting</h2>
<h3 id="common-issues">Common Issues</h3>
<p><strong>1. No Data Showing</strong>
- Check if scraping has been run recently
- Verify date range filters aren't too restrictive
- Try refreshing the page</p>
<p><strong>2. Slow Loading</strong>
- Large date ranges may take time to load
- Try filtering by country or company first
- Check internet connection</p>
<p><strong>3. Report Generation Fails</strong>
- Ensure data exists for selected filters
- Try with smaller date range
- Check browser download settings</p>
<p><strong>4. Scraping Jobs Fail</strong>
- Website may be temporarily unavailable
- Check admin panel for error details
- Try manual scraping again later</p>
<h3 id="browser-compatibility">Browser Compatibility</h3>
<p><strong>Supported Browsers</strong>:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+</p>
<p><strong>Required Features</strong>:
- JavaScript enabled
- Cookies enabled
- Modern CSS support</p>
<h3 id="performance-tips">Performance Tips</h3>
<p><strong>For Better Performance</strong>:
- Use specific date ranges instead of "all time"
- Filter by country/company when possible
- Close unused browser tabs
- Clear browser cache if issues persist</p>
<h3 id="getting-help">Getting Help</h3>
<p><strong>Self-Service Options</strong>:
1. Check this user guide
2. Review error messages carefully
3. Try refreshing the page
4. Clear browser cache and cookies</p>
<p><strong>Contact Support</strong>:
- Check application logs (admin users)
- Report issues with specific error messages
- Include browser and operating system information
- Provide steps to reproduce the issue</p>
<h3 id="data-export-tips">Data Export Tips</h3>
<p><strong>Excel Reports</strong>:
- Use filters to limit data size
- Reports work best with &lt; 10,000 records
- Include date ranges for trend analysis
- Save reports locally for offline analysis</p>
<p><strong>CSV Export</strong> (if available):
- Better for large datasets
- Import into other analysis tools
- Preserves all data formatting
- Smaller file sizes than Excel</p>
<h3 id="mobile-usage">Mobile Usage</h3>
<p><strong>Mobile-Friendly Features</strong>:
- Responsive design adapts to screen size
- Touch-friendly interface elements
- Simplified navigation on small screens
- Core functionality available on mobile</p>
<p><strong>Mobile Limitations</strong>:
- Excel report generation may be slower
- Large datasets may impact performance
- Admin panel optimized for desktop use</p>
<p>For additional help or feature requests, contact your system administrator or check the project documentation.</p>
</body>
</html>