from io import BytesIO
from matplotlib.offsetbox import AnnotationBbox, OffsetImage
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import math
import seaborn as sns

def generate_price_barplot(filtered_df, value_col, title, flag_dir='flags', image_zoom=0.25):

    filtered_df = filter_non_empty_bottle_types(filtered_df, group_col='type')

    plt.figure(figsize=(16, 9))
    order_list = sorted(filtered_df['type'].unique())
    ax = sns.barplot(x="type", y=value_col, hue="country", palette='Greens_r', data=filtered_df, order=order_list, err_kws={'linewidth': 0})
    plt.xlabel("Bottle Type")
    plt.ylabel("Price (€)" if value_col == 'price' else "Price per KG (€)")
    plt.title(title)
    plt.ylim(0, math.ceil(ax.get_ylim()[1]))

    for container in ax.containers:
        ax.bar_label(container, fmt='%.2f€')

    legend_handles, legend_labels = ax.get_legend_handles_labels()

    for container, country in zip(ax.containers, legend_labels):
        try:
            img = mpimg.imread(f"{flag_dir}/{country}.png")
            imagebox = OffsetImage(img, zoom=image_zoom)
        except FileNotFoundError:
            continue  # Skip flag if not found

        for bar in container:
            height = bar.get_height()
            x = bar.get_x() + bar.get_width() / 2.
            y = height
            ab = AnnotationBbox(
                imagebox,
                (x, y),
                frameon=False,
                xycoords='data',
                boxcoords="offset points",
                box_alignment=(0.5, 0),
                xybox=(0, 15)
            )
            ax.add_artist(ab)

    img_buffer = BytesIO()
    plt.tight_layout()
    plt.savefig(img_buffer, format='png')
    plt.close()
    img_buffer.seek(0)
    return img_buffer

def filter_non_empty_bottle_types(df, group_col='type'):
    # Keep only 'type' groups that have more than 0 rows
    non_empty_types = df[group_col].value_counts()
    valid_types = non_empty_types[non_empty_types > 0].index.tolist()
    return df[df[group_col].isin(valid_types)]

def determine_bottle_type(product_type: str, weight: float) -> str:
    """
    Determine the bottle type based on product type and weight.

    Args:
        product_type (str): Type of the product ("Light" or other)
        weight (float): Weight of the bottle in kg

    Returns:
        str: Bottle type classification (T3, T4, T5, or "No Type")
    """
    if weight > 8 and weight <= 15:
        if product_type == "Light":
            return "T4"
        return "T3"
    elif weight > 15 and product_type != "Light":
        return "T5"
    return "No Type"